//
// Created by <PERSON><PERSON> on 2025/8/27 19:21.
//

#ifndef LVPAGEHUB_TEST_H
#define LVPAGEHUB_TEST_H

#include "page_manager.h"
#include "test_view.h"

/**
 * @brief 表盘主结构
 *
 * 继承页面基类，实现MVP模式的表盘页面
 */
typedef struct {
    pm_page_base_t base; // 页面基类（必须是第一个成员）

    test_view_t view; // 视图组件
    // dialplate_model_c_t model;              // 模型组件

    lv_timer_t *timer; // 更新定时器
    // dialplate_record_state_t rec_state;     // 录制状态
    lv_obj_t *last_focus; // 最后焦点对象
} test_t;


/* 页面生命周期回调（实现PageBase虚函数） */
void test_on_custom_attr_config(pm_page_base_t *self);

void test_on_view_load(pm_page_base_t *self);

void test_on_view_did_load(pm_page_base_t *self);

void test_on_view_will_appear(pm_page_base_t *self);

void test_on_view_did_appear(pm_page_base_t *self);

void test_on_view_will_disappear(pm_page_base_t *self);

void test_on_view_did_disappear(pm_page_base_t *self);

void test_on_view_unload(pm_page_base_t *self);

void test_on_view_did_unload(pm_page_base_t *self);

#endif //LVPAGEHUB_TEST_H
