//
// Created by <PERSON><PERSON> on 2025/8/27 19:21.
//

#include "test_view.h"

/* ========== 表盘视图实现 ========== */

/**
 * @brief 创建表盘视图对象
 * @retval 视图对象指针，失败返回NULL
 */
test_view_t *test_view_create() {
    test_view_t *view = (test_view_t *) malloc(sizeof(test_view_t));
    if (view == NULL) {
        PM_LOG_ERROR("Failed to allocate memory for test view");
        return NULL;
    }

    /* 初始化成员变量 */
    memset(view, 0, sizeof(test_view_t));

    PM_LOG_INFO("test view created");
    return view;
}

/**
 * @brief 销毁表盘视图对象
 * @param view 视图对象指针
 */
void test_view_destroy(test_view_t *view) {
    if (view == NULL) {
        return;
    }

    /* 删除UI */
    test_view_delete_ui(view);

    free(view);
    PM_LOG_INFO("test view destroyed");
}

/**
 * @brief 删除表盘视图UI
 * @param view 视图对象指针
 */
void test_view_delete_ui(test_view_t *view) {
    if (view == NULL) {
        return;
    }

    /* 删除动画时间线 */
    if (view->ui.anim_timeline) {
        lv_anim_timeline_delete(view->ui.anim_timeline);
        view->ui.anim_timeline = NULL;
    }

    /* UI对象会随着根对象一起被删除，这里只需要清空指针 */
    memset(&view->ui, 0, sizeof(test_view_ui_t));

    PM_LOG_INFO("test view UI deleted");
}

/**
 * @brief 创建表盘视图UI
 * @param view 视图对象指针
 * @param root 根对象
 */
void test_view_create_ui(test_view_t *view, lv_obj_t *root) {
    if (view == NULL || root == NULL) {
        return;
    }

    /* 创建各个UI组件 */
    test_view_bottom_info_create(view, root);
    test_view_top_info_create(view, root);
    test_view_btn_cont_create(view, root);

    /* 创建动画时间线 */
    view->ui.anim_timeline = lv_anim_timeline_create();

    /* 定义动画宏 */
#define ANIM_DEF(start_time, obj, attr, start, end) \
{start_time, obj, LV_ANIM_EXEC(attr), start, end, 500, lv_anim_path_ease_out, true}

#define ANIM_OPA_DEF(start_time, obj) \
ANIM_DEF(start_time, obj, opa_scale, LV_OPA_TRANSP, LV_OPA_COVER)

    /* 获取目标位置 */
    lv_coord_t y_tar_top = lv_obj_get_y(view->ui.top_info.cont);
    lv_coord_t y_tar_bottom = lv_obj_get_y(view->ui.bottom_info.cont);
    lv_coord_t h_tar_btn = lv_obj_get_height(view->ui.btn_cont.btn_rec);

    /* 定义动画序列 */
    lv_anim_timeline_wrapper_t wrapper[] = {
            /* 顶部信息从上方滑入 */
            ANIM_DEF(0, view->ui.top_info.cont, y, -lv_obj_get_height(view->ui.top_info.cont), y_tar_top),

            /* 底部信息从下方滑入并淡入 */
            ANIM_DEF(200, view->ui.bottom_info.cont, y, -lv_obj_get_height(view->ui.bottom_info.cont), y_tar_bottom),
            ANIM_OPA_DEF(200, view->ui.bottom_info.cont),

            /* 按钮依次展开 */
            ANIM_DEF(500, view->ui.btn_cont.btn_map, height, 0, h_tar_btn),
            ANIM_DEF(600, view->ui.btn_cont.btn_rec, height, 0, h_tar_btn),
            ANIM_DEF(700, view->ui.btn_cont.btn_menu, height, 0, h_tar_btn),
            LV_ANIM_TIMELINE_WRAPPER_END
    };

    /* 添加动画到时间线 */
    lv_anim_timeline_add_wrapper(view->ui.anim_timeline, wrapper);

    PM_LOG_INFO("test view UI created");
}
