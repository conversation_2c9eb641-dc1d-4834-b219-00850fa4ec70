//
// Created by <PERSON><PERSON> on 2025/8/26 17:38.
//

#ifndef LVPAGEHUB_PAGE_MANAGER_H
#define LVPAGEHUB_PAGE_MANAGER_H

#include "lvgl.h"
#include "pm_log.h"
#include "pm_base.h"

// 屏幕宽度
#define SCREEN_WIDTH  (600)
// 屏幕高度
#define SCREEN_HEIGHT (480)
// 屏幕宽度
#define SIM_SCREEN_WIDTH  (600+20)
// 屏幕高度
#define SIM_SCREEN_HEIGHT (480+43)

/* 页面管理器配置 */
#define PM_MAX_PAGE_POOL_SIZE    32    // 最大页面池大小
#define PM_MAX_PAGE_STACK_SIZE   16    // 最大页面堆栈大小
#define PM_MAX_PAGE_NAME_LEN     32    // 最大页面名称长度

#define PM_EMPTY_PAGE_NAME       "EMPTY"

/* 页面动画默认参数 */
#define PM_ANIM_TIME_DEFAULT     500   // 默认动画时间(ms)
#define PM_ANIM_PATH_DEFAULT     lv_anim_path_ease_out

/* 生成stash区域数据 */
#define PM_STASH_MAKE(data) {&(data), sizeof(data)}

/* 从stash区域获取数据 */
#define PM_STASH_POP(page, data) pm_page_stash_pop(page, &(data), sizeof(data))

/* 前向声明 */
typedef struct pm_page_manager pm_page_manager_t;
typedef struct pm_page_base pm_page_base_t;
typedef struct pm_page_factory pm_page_factory_t;

/* 页面状态枚举 */
typedef enum {
    PM_PAGE_STATE_IDLE,
    PM_PAGE_STATE_LOAD,
    PM_PAGE_STATE_WILL_APPEAR,
    PM_PAGE_STATE_DID_APPEAR,
    PM_PAGE_STATE_ACTIVITY,
    PM_PAGE_STATE_WILL_DISAPPEAR,
    PM_PAGE_STATE_DID_DISAPPEAR,
    PM_PAGE_STATE_UNLOAD,
    _PM_PAGE_STATE_LAST
} pm_page_state_t;

/* 页面切换动画类型 */
typedef enum {
    /* 默认（全局）动画类型 */
    PM_LOAD_ANIM_GLOBAL = 0,

    /* 新页面覆盖旧页面 */
    PM_LOAD_ANIM_OVER_LEFT,   // 从左侧覆盖
    PM_LOAD_ANIM_OVER_RIGHT,  // 从右侧覆盖
    PM_LOAD_ANIM_OVER_TOP,    // 从顶部覆盖
    PM_LOAD_ANIM_OVER_BOTTOM, // 从底部覆盖

    /* 新页面推动旧页面 */
    PM_LOAD_ANIM_MOVE_LEFT,   // 向左推动
    PM_LOAD_ANIM_MOVE_RIGHT,  // 向右推动
    PM_LOAD_ANIM_MOVE_TOP,    // 向上推动
    PM_LOAD_ANIM_MOVE_BOTTOM, // 向下推动

    /* 新界面淡入，旧页面淡出 */
    PM_LOAD_ANIM_FADE_ON,

    /* 无动画 */
    PM_LOAD_ANIM_NONE,

    _PM_LOAD_ANIM_LAST = PM_LOAD_ANIM_NONE
} pm_load_anim_t;

/* 页面拖拽方向 */
typedef enum {
    PM_ROOT_DRAG_DIR_NONE, // 无拖拽方向
    PM_ROOT_DRAG_DIR_HOR,  // 水平拖拽
    PM_ROOT_DRAG_DIR_VER,  // 垂直拖拽
} pm_root_drag_dir_t;

/* Stash数据区域 */
typedef struct {
    void *ptr;
    uint32_t size;
} pm_stash_t;

/* 页面切换动画属性 */
typedef struct {
    uint8_t type;
    uint16_t time;
    lv_anim_path_cb_t path;
} pm_anim_attr_t;

/* 动画设置器和获取器函数指针 */
typedef void (*pm_anim_setter_t)(void *, int32_t);

typedef int32_t (*pm_anim_getter_t)(void *);

/* 动画值结构 */
typedef struct {
    /* 作为进入方 */
    struct {
        int32_t start; // 动画起始值
        int32_t end;   // 动画结束值
    } enter;

    /* 作为退出方 */
    struct {
        int32_t start; // 动画起始值
        int32_t end;   // 动画结束值
    } exit;
} pm_anim_value_t;

/* 页面加载动画属性 */
typedef struct {
    pm_anim_setter_t setter;     // 动画设置器函数
    pm_anim_getter_t getter;     // 动画获取器函数
    pm_root_drag_dir_t drag_dir; // 拖拽方向
    pm_anim_value_t push;        // 推入动画值
    pm_anim_value_t pop;         // 弹出动画值
} pm_load_anim_attr_t;

/* 页面基类虚函数表 - 页面生命周期回调函数 */
typedef struct {
    void (*on_custom_attr_config)(pm_page_base_t *self);  // 自定义属性配置
    void (*on_view_load)(pm_page_base_t *self);           // 页面加载，创建UI
    void (*on_view_did_load)(pm_page_base_t *self);       // 页面加载完成
    void (*on_view_will_appear)(pm_page_base_t *self);    // 页面即将显示，启动定时器
    void (*on_view_did_appear)(pm_page_base_t *self);     // 页面已显示，开始交互
    void (*on_view_will_disappear)(pm_page_base_t *self); // 页面即将消失，保存状态
    void (*on_view_did_disappear)(pm_page_base_t *self);  // 页面已消失，清理资源
    void (*on_view_unload)(pm_page_base_t *self);         // 页面开始卸载
    void (*on_view_did_unload)(pm_page_base_t *self);     // 页面卸载完成
} pm_page_vtable_t;

/* 页面基类结构 */
struct pm_page_base {
    /* 公共成员 */
    lv_obj_t *root;                  // UI根节点
    pm_page_manager_t *manager;      // 页面管理器指针
    char name[PM_MAX_PAGE_NAME_LEN]; // 页面名称
    uint16_t id;                     // 页面ID
    void *user_data;                 // 用户数据指针

    /* 虚函数表 */
    pm_page_vtable_t *vtable;

    /* 私有数据，仅页面管理器访问 */
    struct {
        // bool req_enable_cache;       // 缓存启用请求
        // bool req_disable_auto_cache; // 自动缓存管理启用请求

        // bool is_disable_auto_cache; // 是否为自动缓存管理
        // bool is_cached;             // 缓存启用

        // pm_stash_t stash;      // Stash区域
        pm_page_state_t state; // 页面状态

        /* 动画状态 */
        struct {
            bool is_enter;       // 是否为进入方
            bool is_busy;        // 动画是否正在播放
            pm_anim_attr_t attr; // 动画属性
        } anim;
    } priv;
};

typedef struct {
    // 管理器主体
    lv_obj_t *src;
    // 默认样式
    lv_style_t default_style;
} PageManager_t;

extern PageManager_t PageManager;

#endif //LVPAGEHUB_PAGE_MANAGER_H
