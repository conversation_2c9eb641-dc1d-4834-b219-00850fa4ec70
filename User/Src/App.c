//
// Created by <PERSON><PERSON> on 2025/8/27 15:41.
//

#include "App.h"

#include "page_manager.h"

// #ifdef _WIN32
// #include <windows.h>
// #endif

/**
 * @brief 初始化显示系统
 * @retval 0：成功，负数：错误码
 */
static int app_root_init() {
    // 初始化样式
    lv_style_init(&PageManager.default_style);
    // 设置内边距为 0
    lv_style_set_pad_all(&PageManager.default_style, 0);
    // 设置背景不透明度为 0
    // lv_style_set_bg_opa(&PageManager.default_style, LV_OPA_0);
    // 设置为直角边
    lv_style_set_radius(&PageManager.default_style, 0);
    // 设置为无边框
    lv_style_set_border_width(&PageManager.default_style, 0);
    // 设置对齐方式为居中
    lv_style_set_align(&PageManager.default_style, LV_ALIGN_CENTER);

    PageManager.src = lv_screen_active();
    lv_obj_add_style(PageManager.src, &PageManager.default_style, 0);
    lv_obj_set_size(PageManager.src, SCREEN_WIDTH, SCREEN_HEIGHT);
    lv_obj_remove_flag(PageManager.src, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(PageManager.src, LV_SCROLLBAR_MODE_OFF);
    lv_obj_set_style_bg_color(PageManager.src, lv_color_black(), 0);
    return 0;
}

void user_ui_init() {
    app_root_init();
}

#if 0
/**
 * @brief 计算Windows窗口装饰大小并调整窗口以获得指定的客户区域大小
 * @param display LVGL显示对象
 * @param client_width 期望的客户区域宽度
 * @param client_height 期望的客户区域高度
 */
void app_adjust_window_size(lv_display_t *display, const int client_width, const int client_height) {

#ifdef _WIN32
if (!display) return;

// 获取窗口句柄
HWND hwnd = lv_windows_get_display_window_handle(display);
    if (!hwnd) return;

// 获取当前窗口和客户区域大小
RECT window_rect, client_rect;
GetWindowRect(hwnd, &window_rect);
GetClientRect(hwnd, &client_rect);

// 计算窗口装饰大小
const int decoration_width = (window_rect.right - window_rect.left) - (client_rect.right - client_rect.left);
const int decoration_height = (window_rect.bottom - window_rect.top) - (client_rect.bottom - client_rect.top);

// 计算新的窗口大小
const int new_window_width = client_width + decoration_width;
const int new_window_height = client_height + decoration_height;

// 调整窗口大小
SetWindowPos(hwnd, NULL, 0, 0, new_window_width, new_window_height,
             SWP_NOZORDER| SWP_NOACTIVATE| SWP_NOMOVE);

// 更新LVGL显示分辨率为客户区域大小
lv_display_set_resolution(display, client_width, client_height);
#endif
}
#endif
