//
// Created by <PERSON><PERSON> on 2025/8/27 15:41.
//

#include "App.h"

#include "page_manager.h"

/**
 * @brief 初始化显示系统
 * @param config 配置指针
 * @retval 0：成功，负数：错误码
 */
static int app_root_init() {
    // 初始化样式
    lv_style_init(&PageManager.default_style);
    // 设置内边距为 0
    lv_style_set_pad_all(&PageManager.default_style, 0);
    // 设置背景不透明度为 0
    // lv_style_set_bg_opa(&PageManager.default_style, LV_OPA_0);
    // 设置为直角边
    lv_style_set_radius(&PageManager.default_style, 0);
    // 设置为无边框
    lv_style_set_border_width(&PageManager.default_style, 0);
    // 设置对齐方式为居中
    lv_style_set_align(&PageManager.default_style, LV_ALIGN_CENTER);

    PageManager.src = lv_obj_create(lv_scr_act());
    lv_obj_add_style(PageManager.src, &PageManager.default_style, 0);
    lv_obj_set_size(PageManager.src, SCREEN_WIDTH, SCREEN_HEIGHT);
    lv_obj_remove_flag(PageManager.src, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(PageManager.src, LV_SCROLLBAR_MODE_OFF);
    lv_obj_set_style_bg_color(PageManager.src, lv_color_black(), 0);
    return 0;
}

void user_ui_init() {
    app_root_init();
}
