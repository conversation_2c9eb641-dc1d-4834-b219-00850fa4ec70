/**
 * @file lv_obj_ext_func.h
 * @brief LVGL对象扩展功能模块
 *
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

#ifndef __LV_OBJ_EXT_FUNC_H
#define __LV_OBJ_EXT_FUNC_H

#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl.h"

/*********************
 *      宏定义
 *********************/

/** 默认动画时间（毫秒） */
#define LV_ANIM_TIME_DEFAULT    400

/** 动画执行回调函数生成宏 */
#define LV_ANIM_EXEC(attr)      (lv_anim_exec_xcb_t)lv_obj_set_##attr

/*********************
 *      函数声明
 *********************/

/**
 * @brief 设置对象的透明度缩放
 * @param obj 目标对象
 * @param opa 透明度值 (0-255)
 */
void lv_obj_set_opa_scale(lv_obj_t *obj, int16_t opa);

/**
 * @brief 获取对象的透明度缩放
 * @param obj 目标对象
 * @return 透明度值 (0-255)
 */
int16_t lv_obj_get_opa_scale(lv_obj_t *obj);

/**
 * @brief 在标签末尾追加文本
 * @param label 标签对象
 * @param text 要追加的文本
 */
void lv_label_set_text_add(lv_obj_t *label, const char *text);

/**
 * @brief 为对象添加动画（完整参数版本）
 * @param obj 目标对象
 * @param a 动画结构体指针，可以为NULL（使用内部临时变量）
 * @param exec_cb 动画执行回调函数
 * @param start 动画起始值
 * @param end 动画结束值
 * @param time 动画持续时间（毫秒）
 * @param delay 动画延迟时间（毫秒）
 * @param ready_cb 动画完成回调函数，可以为NULL
 * @param path_cb 动画路径函数（缓动函数）
 */
void lv_obj_add_anim(
        lv_obj_t *obj,
        lv_anim_t *a,
        lv_anim_exec_xcb_t exec_cb,
        int32_t start,
        int32_t end,
        uint16_t time,
        uint32_t delay,
        lv_anim_ready_cb_t ready_cb,
        lv_anim_path_cb_t path_cb
);

/**
 * @brief 为对象添加动画（简化版本，使用默认参数）
 * @param obj 目标对象
 * @param exec_cb 动画执行回调函数
 * @param start 动画起始值
 * @param end 动画结束值
 * @param time 动画持续时间（毫秒）
 */
void lv_obj_add_anim_simple(
        lv_obj_t *obj,
        lv_anim_exec_xcb_t exec_cb,
        int32_t start,
        int32_t end,
        uint16_t time
);

/**
 * @brief 为对象添加带延迟的动画
 * @param obj 目标对象
 * @param exec_cb 动画执行回调函数
 * @param start 动画起始值
 * @param end 动画结束值
 * @param time 动画持续时间（毫秒）
 * @param delay 动画延迟时间（毫秒）
 */
void lv_obj_add_anim_delay(
        lv_obj_t *obj,
        lv_anim_exec_xcb_t exec_cb,
        int32_t start,
        int32_t end,
        uint16_t time,
        uint32_t delay
);

/*********************
 *      便捷宏定义
 *********************/

/**
 * @brief 快速为对象属性添加动画的宏
 * @param obj 目标对象
 * @param attr 对象属性名（如x, y, width等）
 * @param target 目标值
 * @param time 动画时间（毫秒）
 *
 * 使用示例：
 * LV_OBJ_ADD_ANIM(my_obj, x, 100, 500); // 将对象x坐标动画到100，耗时500ms
 */
#define LV_OBJ_ADD_ANIM(obj, attr, target, time) \
do { \
    lv_obj_add_anim_simple( \
        (obj), \
        (lv_anim_exec_xcb_t)lv_obj_set_##attr, \
        lv_obj_get_##attr(obj), \
        (target), \
        (time) \
    ); \
} while(0)

/**
 * @brief 快速为对象属性添加延迟动画的宏
 * @param obj 目标对象
 * @param attr 对象属性名（如x, y, width等）
 * @param target 目标值
 * @param delay 延迟时间（毫秒）
 * @param time 动画时间（毫秒）
 *
 * 使用示例：
 * LV_OBJ_ADD_DELAY_ANIM(my_obj, y, 200, 100, 400); // 延迟100ms后将y坐标动画到200，耗时400ms
 */
#define LV_OBJ_ADD_DELAY_ANIM(obj, attr, target, delay, time) \
do { \
    lv_obj_add_anim_delay( \
        (obj), \
        (lv_anim_exec_xcb_t)lv_obj_set_##attr, \
        lv_obj_get_##attr(obj), \
        (target), \
        (time), \
        (delay) \
    ); \
} while(0)

/**
 * @brief 根据类型获取输入设备
 * @param type 输入设备类型
 * @return 找到的输入设备指针，未找到返回NULL
 */
lv_indev_t *lv_get_indev(lv_indev_type_t type);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*__LV_OBJ_EXT_FUNC_H*/
